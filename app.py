import streamlit as st
import os
from config.settings import settings
from utils.file_handlers import FileHandler
from utils.streamlit_components import StreamlitComponents
from utils.crew_orchestrator import CrewOrchestrator

# Page configuration
st.set_page_config(
    page_title=settings.PAGE_TITLE,
    page_icon=settings.PAGE_ICON,
    layout=settings.LAYOUT,
    initial_sidebar_state="expanded"
)

def main():
    """Main application function"""
    
    # Initialize session state
    if 'processing_complete' not in st.session_state:
        st.session_state.processing_complete = False
    if 'validation_result' not in st.session_state:
        st.session_state.validation_result = None
    if 'json_output' not in st.session_state:
        st.session_state.json_output = None
    if 'display_data' not in st.session_state:
        st.session_state.display_data = None
    if 'file_content' not in st.session_state:
        st.session_state.file_content = None
    if 'filename' not in st.session_state:
        st.session_state.filename = None
    
    # Create sidebar
    StreamlitComponents.create_sidebar_info()
    
    # Main title
    st.title("📄 Agentic Document Extraction System")
    st.markdown("Upload your portfolio documents and let our AI agents extract the data intelligently!")
    
    # Check for OpenAI API key
    if not settings.OPENAI_API_KEY:
        st.error("⚠️ OpenAI API key not found! Please set the OPENAI_API_KEY environment variable.")
        st.info("You can set it in a .env file or as an environment variable.")
        st.stop()
    
    # Initialize components
    file_handler = FileHandler()
    components = StreamlitComponents()
    
    # File upload section
    st.header("📁 Upload Document")
    file_content, filename = file_handler.handle_file_upload()
    
    if file_content and filename:
        # Store in session state
        st.session_state.file_content = file_content
        st.session_state.filename = filename
        
        # Display file information
        file_info = file_handler.get_file_info(file_content, filename)
        components.display_file_info(file_info)
        
        # Create two columns for preview and processing
        col1, col2 = st.columns([1, 1])
        
        with col1:
            # Document preview
            preview_data = file_handler.create_document_preview(file_content, filename)
            file_handler.display_document_preview(preview_data, filename)
        
        with col2:
            # Processing section
            st.subheader("🚀 Process Document")
            
            if st.button("Start Processing", type="primary", use_container_width=True):
                process_document(file_content, filename)
    
    # Display results if processing is complete
    if st.session_state.processing_complete and st.session_state.validation_result:
        display_results()

def process_document(file_content: bytes, filename: str):
    """Process the document through the agent workflow"""
    
    # Initialize orchestrator
    orchestrator = CrewOrchestrator()
    orchestrator.reset_status()
    
    # Create placeholder for status updates
    status_placeholder = st.empty()
    progress_placeholder = st.empty()
    
    try:
        with st.spinner("Processing document through AI agents..."):
            # Show initial status
            with status_placeholder.container():
                StreamlitComponents.display_processing_status(orchestrator.get_status_display())
            
            # Process document
            validation_result, json_output, final_status = orchestrator.process_document(
                file_content, filename
            )
            
            # Update final status
            with status_placeholder.container():
                StreamlitComponents.display_processing_status(orchestrator.get_status_display())
            
            # Store results in session state
            st.session_state.validation_result = validation_result
            st.session_state.json_output = json_output
            st.session_state.processing_complete = True
            
            # Prepare display data
            if validation_result.processed_data:
                from tools.validation_tools import DataValidator
                validator = DataValidator()
                st.session_state.display_data = validator.format_for_display(validation_result.processed_data)
            
            # Show completion message
            if validation_result.is_valid:
                StreamlitComponents.display_success_message("Document processing completed successfully!")
            else:
                StreamlitComponents.display_error_message("Document processing completed with validation errors.")
    
    except Exception as e:
        StreamlitComponents.display_error_message(f"Processing failed: {str(e)}")
        st.session_state.processing_complete = False

def display_results():
    """Display processing results"""
    
    st.header("📊 Extraction Results")
    
    # Create tabs for different views
    tab1, tab2, tab3, tab4 = st.tabs(["📋 Summary", "💼 Holdings", "✅ Validation", "📄 JSON Output"])
    
    with tab1:
        # Display metadata and summary
        if st.session_state.display_data and st.session_state.display_data.get('metadata'):
            StreamlitComponents.display_metadata_table(st.session_state.display_data['metadata'])
        else:
            st.info("No document metadata extracted")
    
    with tab2:
        # Display holdings table
        if st.session_state.display_data and st.session_state.display_data.get('holdings'):
            StreamlitComponents.display_holdings_table(st.session_state.display_data['holdings'])
        else:
            st.warning("No portfolio holdings found")
    
    with tab3:
        # Display validation results
        if st.session_state.validation_result:
            StreamlitComponents.display_validation_status(st.session_state.validation_result)
    
    with tab4:
        # Display JSON output
        if st.session_state.json_output:
            StreamlitComponents.display_json_output(st.session_state.json_output)
    
    # Add reset button
    st.markdown("---")
    if st.button("🔄 Process Another Document", use_container_width=True):
        # Reset session state
        st.session_state.processing_complete = False
        st.session_state.validation_result = None
        st.session_state.json_output = None
        st.session_state.display_data = None
        st.session_state.file_content = None
        st.session_state.filename = None
        st.rerun()

if __name__ == "__main__":
    main()
