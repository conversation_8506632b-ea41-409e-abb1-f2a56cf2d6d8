import os
from dotenv import load_dotenv

load_dotenv()

class Settings:
    # OpenAI Configuration
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    OPENAI_MODEL = "gpt-4-vision-preview"
    
    # Document Processing
    CHUNK_SIZE = 5  # Pages per chunk
    SUPPORTED_FORMATS = [".pdf", ".docx", ".png", ".jpg", ".jpeg"]
    MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
    
    # Streamlit Configuration
    PAGE_TITLE = "Document Extraction Agent"
    PAGE_ICON = "📄"
    LAYOUT = "wide"
    
    # CrewAI Configuration
    CREW_VERBOSE = True
    
    # Required Fields for Validation
    REQUIRED_FIELDS = ["ticker_symbol", "shares_quantity"]
    
    # Optional Fields
    OPTIONAL_FIELDS = [
        "document_name", "advisor_name", "client_name", 
        "portfolio_id", "total_account_value", "analysis_date",
        "account_number", "current_value", "cost_basis"
    ]

settings = Settings()
