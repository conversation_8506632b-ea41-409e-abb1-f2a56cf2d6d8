import os
from dotenv import load_dotenv

load_dotenv()

class Settings:
    # Azure OpenAI Configuration (Primary)
    AZURE_OPENAI_API_KEY = os.getenv("AZURE_OPENAI_API_KEY")
    AZURE_OPENAI_ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT")
    AZURE_OPENAI_API_VERSION = os.getenv("AZURE_OPENAI_API_VERSION", "2024-12-01-preview")
    AZURE_OPENAI_DEPLOYMENT_NAME = os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME", "gpt-4o")

    # Azure Document Intelligence Configuration
    AZURE_DOCUMENT_INTELLIGENCE_KEY = os.getenv("AZURE_DOCUMENT_INTELLIGENCE_KEY")
    AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT = os.getenv("AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT")

    # Fallback OpenAI Configuration
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    OPENAI_MODEL = "gpt-4-vision-preview"

    # Determine which service to use
    USE_AZURE = bool(AZURE_OPENAI_API_KEY and AZURE_OPENAI_ENDPOINT)
    USE_AZURE_DOCUMENT_INTELLIGENCE = bool(AZURE_DOCUMENT_INTELLIGENCE_KEY and AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT)

    # Document Processing
    CHUNK_SIZE = 5  # Pages per chunk
    SUPPORTED_FORMATS = [".pdf", ".docx", ".png", ".jpg", ".jpeg"]
    MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB

    # Streamlit Configuration
    PAGE_TITLE = "Document Extraction Agent"
    PAGE_ICON = "📄"
    LAYOUT = "wide"

    # CrewAI Configuration
    CREW_VERBOSE = True

    # Required Fields for Validation
    REQUIRED_FIELDS = ["ticker_symbol", "shares_quantity"]

    # Optional Fields
    OPTIONAL_FIELDS = [
        "document_name", "advisor_name", "client_name",
        "portfolio_id", "total_account_value", "analysis_date",
        "account_number", "current_value", "cost_basis"
    ]

settings = Settings()
