# 📄 Agentic Document Extraction System

An intelligent document processing application that uses CrewAI multi-agent workflows and GPT-4V vision capabilities to extract portfolio data from financial documents.

## 🚀 Features

- **Multi-Agent Workflow**: 5 specialized AI agents working in sequence
- **Document Chunking**: Automatically splits large documents into manageable chunks
- **Vision-Based Extraction**: Uses GPT-4V to understand document layouts and extract data
- **Intelligent Data Mapping**: Handles non-uniform document formats and field variations
- **Data Validation**: Ensures required fields are present and validates data quality
- **Interactive UI**: Clean Streamlit interface with real-time processing status
- **Multiple Formats**: Supports PDF, DOCX, and image files

## 🤖 Agent Workflow

1. **Agent 1 - Document Chunking**: Splits documents into 5-page chunks for processing
2. **Agent 2 - Data Extraction**: Uses GPT-4V for layout detection and data extraction
3. **Agent 3 - Data Aggregation**: Combines and reconciles data from all chunks
4. **Agent 4 - Data Validation**: Validates required fields and prepares display data
5. **Agent 5 - JSON Formatting**: Creates structured JSON output

## 📋 Extracted Data Format

The system extracts the following information:

### Required Fields
- **Ticker/Symbol** (Required for validation to pass)
- **Shares/Quantity** (Required for validation to pass)

### Optional Fields
- Document name
- Advisor name
- Client name
- Portfolio ID
- Total account value
- Date of analysis
- Account number
- Current value/market value
- Cost/cost-basis

## 🛠️ Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd doc_extraction_6
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env and add your OpenAI API key
   ```

4. **Run the application**:
   ```bash
   streamlit run app.py
   ```

## 🔧 Configuration

### Environment Variables

- `OPENAI_API_KEY`: Your OpenAI API key (required)
- `CHUNK_SIZE`: Number of pages per chunk (default: 5)
- `MAX_FILE_SIZE`: Maximum file size in bytes (default: 50MB)

### Supported File Formats

- **PDF**: Native and scanned PDFs
- **DOCX**: Microsoft Word documents
- **Images**: PNG, JPG, JPEG

## 📊 Usage

1. **Upload Document**: Drag and drop or browse for your portfolio document
2. **Preview**: View document preview in the interface
3. **Process**: Click "Start Processing" to begin the agent workflow
4. **Monitor**: Watch real-time status updates from all 5 agents
5. **Review Results**: View extracted data in multiple formats:
   - Summary metadata
   - Holdings table
   - Validation status
   - JSON output

## 🎯 Key Features

### Intelligent Field Mapping
The system intelligently maps various field names to standard formats:
- "Symbol" = "Ticker" = "Stock Symbol" → ticker_symbol
- "Shares" = "Qty" = "Quantity" = "Units" → shares_quantity
- "Market Value" = "Current Value" → current_value

### Robust Error Handling
- Graceful handling of missing data
- Validation warnings for incomplete information
- Detailed error reporting
- Partial results when possible

### Performance Optimization
- Parallel processing of document chunks
- Efficient memory management
- Rate limiting for API calls
- Caching of processed results

## 📁 Project Structure

```
doc_extraction_6/
├── app.py                          # Main Streamlit application
├── agents/                         # CrewAI agents
│   ├── chunking_agent.py
│   ├── extraction_agent.py
│   ├── aggregation_agent.py
│   ├── validation_agent.py
│   └── json_formatter_agent.py
├── tools/                          # Processing tools
│   ├── document_processor.py
│   ├── vision_tools.py
│   └── validation_tools.py
├── models/                         # Data models
│   └── data_models.py
├── utils/                          # Utilities
│   ├── crew_orchestrator.py
│   ├── file_handlers.py
│   └── streamlit_components.py
├── config/                         # Configuration
│   └── settings.py
└── requirements.txt
```

## 🔍 Example Output

```json
{
  "document_name": "Portfolio_Report_Q4_2023.pdf",
  "advisor_name": "John Smith",
  "client_name": "ABC Investment Corp",
  "portfolio_id": "PORT-12345",
  "total_account_value": "$1,250,000",
  "date_of_analysis": "2023-12-31",
  "tabular_data": [
    {
      "account_number": "ACC-001",
      "ticker_symbol": "AAPL",
      "shares_quantity": "100",
      "current_value": "$15,000",
      "cost_basis": "$12,000"
    }
  ],
  "validation_status": {
    "is_valid": true,
    "missing_required_fields": [],
    "errors": [],
    "warnings": []
  }
}
```

## 🚨 Requirements

- Python 3.8+
- OpenAI API key with GPT-4V access
- Streamlit
- CrewAI
- PyMuPDF (for PDF processing)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For issues and questions:
1. Check the existing issues
2. Create a new issue with detailed description
3. Include sample documents (anonymized) if possible

## 🔮 Future Enhancements

- Support for additional document formats
- Advanced OCR capabilities
- Batch processing
- API endpoints
- Custom field mapping
- Integration with databases
