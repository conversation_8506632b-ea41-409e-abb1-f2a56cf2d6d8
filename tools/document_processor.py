import io
import base64
from typing import List, Tu<PERSON>, Optional
from PIL import Image
import PyPDF2
import fitz  # pymupdf
from docx import Document
from models.data_models import DocumentChunk
from config.settings import settings

class DocumentProcessor:
    """Handles processing of different document formats"""
    
    def __init__(self):
        self.chunk_size = settings.CHUNK_SIZE
        
    def process_document(self, file_content: bytes, filename: str) -> List[DocumentChunk]:
        """
        Process uploaded document and return chunks
        """
        file_extension = filename.lower().split('.')[-1]
        
        if file_extension == 'pdf':
            return self._process_pdf(file_content, filename)
        elif file_extension == 'docx':
            return self._process_docx(file_content, filename)
        elif file_extension in ['png', 'jpg', 'jpeg']:
            return self._process_image(file_content, filename)
        else:
            raise ValueError(f"Unsupported file format: {file_extension}")
    
    def _process_pdf(self, file_content: bytes, filename: str) -> List[DocumentChunk]:
        """Process PDF files and create chunks"""
        chunks = []
        
        # Use pymupdf for better image extraction
        pdf_document = fitz.open(stream=file_content, filetype="pdf")
        total_pages = len(pdf_document)
        
        for start_page in range(0, total_pages, self.chunk_size):
            end_page = min(start_page + self.chunk_size - 1, total_pages - 1)
            chunk_id = f"{filename}_chunk_{start_page+1}_{end_page+1}"
            
            # Extract text content
            text_content = ""
            images = []
            
            for page_num in range(start_page, end_page + 1):
                page = pdf_document[page_num]
                text_content += page.get_text() + "\n"
                
                # Convert page to image for vision processing
                pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))  # 2x zoom for better quality
                img_data = pix.tobytes("png")
                img_base64 = base64.b64encode(img_data).decode()
                images.append(img_base64)
            
            # Combine all page images for this chunk
            combined_image = self._combine_images(images) if len(images) > 1 else images[0] if images else None
            
            chunk = DocumentChunk(
                chunk_id=chunk_id,
                page_start=start_page + 1,
                page_end=end_page + 1,
                content=text_content,
                image_data=combined_image,
                metadata={
                    "filename": filename,
                    "total_pages": total_pages,
                    "file_type": "pdf"
                }
            )
            chunks.append(chunk)
        
        pdf_document.close()
        return chunks
    
    def _process_docx(self, file_content: bytes, filename: str) -> List[DocumentChunk]:
        """Process DOCX files and create chunks"""
        doc = Document(io.BytesIO(file_content))
        
        # Extract all text
        full_text = []
        for paragraph in doc.paragraphs:
            full_text.append(paragraph.text)
        
        # For DOCX, we'll treat it as one chunk since page concept is different
        chunk = DocumentChunk(
            chunk_id=f"{filename}_chunk_1_1",
            page_start=1,
            page_end=1,
            content="\n".join(full_text),
            image_data=None,  # TODO: Add image extraction from DOCX if needed
            metadata={
                "filename": filename,
                "total_pages": 1,
                "file_type": "docx"
            }
        )
        
        return [chunk]
    
    def _process_image(self, file_content: bytes, filename: str) -> List[DocumentChunk]:
        """Process image files"""
        # Convert image to base64
        img_base64 = base64.b64encode(file_content).decode()
        
        chunk = DocumentChunk(
            chunk_id=f"{filename}_chunk_1_1",
            page_start=1,
            page_end=1,
            content="",  # No text content for images
            image_data=img_base64,
            metadata={
                "filename": filename,
                "total_pages": 1,
                "file_type": "image"
            }
        )
        
        return [chunk]
    
    def _combine_images(self, image_base64_list: List[str]) -> str:
        """Combine multiple images vertically"""
        if not image_base64_list:
            return None
        
        if len(image_base64_list) == 1:
            return image_base64_list[0]
        
        try:
            images = []
            for img_b64 in image_base64_list:
                img_data = base64.b64decode(img_b64)
                img = Image.open(io.BytesIO(img_data))
                images.append(img)
            
            # Calculate total height and max width
            total_height = sum(img.height for img in images)
            max_width = max(img.width for img in images)
            
            # Create combined image
            combined = Image.new('RGB', (max_width, total_height), 'white')
            
            y_offset = 0
            for img in images:
                combined.paste(img, (0, y_offset))
                y_offset += img.height
            
            # Convert back to base64
            buffer = io.BytesIO()
            combined.save(buffer, format='PNG')
            combined_b64 = base64.b64encode(buffer.getvalue()).decode()
            
            return combined_b64
            
        except Exception as e:
            # If combining fails, return the first image
            return image_base64_list[0]
