import base64
import io
from typing import List, Dict, Any, Optional
from azure.ai.documentintelligence import DocumentIntelligenceClient
from azure.core.credentials import AzureKeyCredential
from config.settings import settings
from models.data_models import DocumentChunk

class AzureDocumentIntelligence:
    """Azure Document Intelligence integration for enhanced document processing"""
    
    def __init__(self):
        if settings.USE_AZURE_DOCUMENT_INTELLIGENCE:
            self.client = DocumentIntelligenceClient(
                endpoint=settings.AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT,
                credential=AzureKeyCredential(settings.AZURE_DOCUMENT_INTELLIGENCE_KEY)
            )
        else:
            self.client = None
    
    def is_available(self) -> bool:
        """Check if Azure Document Intelligence is available"""
        return self.client is not None
    
    def analyze_document(self, file_content: bytes, filename: str) -> List[DocumentChunk]:
        """
        Analyze document using Azure Document Intelligence
        Returns enhanced chunks with better text extraction and layout information
        """
        if not self.is_available():
            raise ValueError("Azure Document Intelligence is not configured")
        
        try:
            # Analyze document with layout model
            poller = self.client.begin_analyze_document(
                "prebuilt-layout",
                analyze_request=file_content,
                content_type="application/octet-stream"
            )
            
            result = poller.result()
            
            # Process the results into chunks
            chunks = self._process_analysis_result(result, filename)
            
            return chunks
            
        except Exception as e:
            print(f"Azure Document Intelligence analysis failed: {str(e)}")
            return []
    
    def _process_analysis_result(self, result, filename: str) -> List[DocumentChunk]:
        """Process Azure Document Intelligence results into chunks"""
        chunks = []
        
        if not result.pages:
            return chunks
        
        # Group pages into chunks
        total_pages = len(result.pages)
        chunk_size = settings.CHUNK_SIZE
        
        for start_page in range(0, total_pages, chunk_size):
            end_page = min(start_page + chunk_size - 1, total_pages - 1)
            chunk_id = f"{filename}_azure_chunk_{start_page+1}_{end_page+1}"
            
            # Extract text and tables for this chunk
            chunk_content = self._extract_chunk_content(
                result, start_page, end_page
            )
            
            # Create chunk
            chunk = DocumentChunk(
                chunk_id=chunk_id,
                page_start=start_page + 1,
                page_end=end_page + 1,
                content=chunk_content["text"],
                image_data=None,  # Azure DI doesn't provide images directly
                metadata={
                    "filename": filename,
                    "total_pages": total_pages,
                    "file_type": "azure_analyzed",
                    "tables": chunk_content["tables"],
                    "key_value_pairs": chunk_content["key_value_pairs"],
                    "confidence_scores": chunk_content["confidence_scores"]
                }
            )
            
            chunks.append(chunk)
        
        return chunks
    
    def _extract_chunk_content(self, result, start_page: int, end_page: int) -> Dict[str, Any]:
        """Extract content for a specific chunk of pages"""
        content = {
            "text": "",
            "tables": [],
            "key_value_pairs": [],
            "confidence_scores": []
        }
        
        # Extract text from pages
        page_texts = []
        for page_idx in range(start_page, end_page + 1):
            if page_idx < len(result.pages):
                page = result.pages[page_idx]
                
                # Extract lines of text
                page_text = ""
                if hasattr(page, 'lines') and page.lines:
                    for line in page.lines:
                        page_text += line.content + "\n"
                        if hasattr(line, 'confidence'):
                            content["confidence_scores"].append(line.confidence)
                
                page_texts.append(page_text)
        
        content["text"] = "\n".join(page_texts)
        
        # Extract tables that fall within this chunk
        if hasattr(result, 'tables') and result.tables:
            for table in result.tables:
                # Check if table is in our page range
                if hasattr(table, 'bounding_regions') and table.bounding_regions:
                    table_pages = [br.page_number for br in table.bounding_regions]
                    if any(start_page + 1 <= page <= end_page + 1 for page in table_pages):
                        table_data = self._extract_table_data(table)
                        content["tables"].append(table_data)
        
        # Extract key-value pairs
        if hasattr(result, 'key_value_pairs') and result.key_value_pairs:
            for kvp in result.key_value_pairs:
                if hasattr(kvp, 'key') and hasattr(kvp, 'value'):
                    # Check if KVP is in our page range
                    kvp_pages = []
                    if hasattr(kvp.key, 'bounding_regions'):
                        kvp_pages.extend([br.page_number for br in kvp.key.bounding_regions])
                    if hasattr(kvp.value, 'bounding_regions'):
                        kvp_pages.extend([br.page_number for br in kvp.value.bounding_regions])
                    
                    if any(start_page + 1 <= page <= end_page + 1 for page in kvp_pages):
                        content["key_value_pairs"].append({
                            "key": kvp.key.content if kvp.key else "",
                            "value": kvp.value.content if kvp.value else "",
                            "confidence": getattr(kvp, 'confidence', 0.0)
                        })
        
        return content
    
    def _extract_table_data(self, table) -> Dict[str, Any]:
        """Extract structured data from a table"""
        table_data = {
            "row_count": getattr(table, 'row_count', 0),
            "column_count": getattr(table, 'column_count', 0),
            "cells": [],
            "content": getattr(table, 'content', ''),
            "confidence": getattr(table, 'confidence', 0.0)
        }
        
        if hasattr(table, 'cells') and table.cells:
            for cell in table.cells:
                cell_data = {
                    "content": getattr(cell, 'content', ''),
                    "row_index": getattr(cell, 'row_index', 0),
                    "column_index": getattr(cell, 'column_index', 0),
                    "row_span": getattr(cell, 'row_span', 1),
                    "column_span": getattr(cell, 'column_span', 1),
                    "confidence": getattr(cell, 'confidence', 0.0),
                    "kind": getattr(cell, 'kind', 'content')
                }
                table_data["cells"].append(cell_data)
        
        return table_data
    
    def extract_financial_data_from_tables(self, chunks: List[DocumentChunk]) -> List[Dict[str, Any]]:
        """
        Extract financial/portfolio data specifically from Azure DI table results
        """
        financial_data = []
        
        for chunk in chunks:
            if chunk.metadata.get("tables"):
                for table in chunk.metadata["tables"]:
                    # Analyze table structure for financial data
                    table_financial_data = self._analyze_financial_table(table)
                    if table_financial_data:
                        financial_data.extend(table_financial_data)
        
        return financial_data
    
    def _analyze_financial_table(self, table: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Analyze a table for financial/portfolio data"""
        if not table.get("cells"):
            return []
        
        # Create a grid representation of the table
        rows = table.get("row_count", 0)
        cols = table.get("column_count", 0)
        
        if rows == 0 or cols == 0:
            return []
        
        # Initialize grid
        grid = [["" for _ in range(cols)] for _ in range(rows)]
        
        # Fill grid with cell content
        for cell in table["cells"]:
            row_idx = cell.get("row_index", 0)
            col_idx = cell.get("column_index", 0)
            content = cell.get("content", "").strip()
            
            if row_idx < rows and col_idx < cols:
                grid[row_idx][col_idx] = content
        
        # Look for financial data patterns
        financial_records = []
        
        # Find header row (usually first row)
        if rows > 1:
            headers = [cell.lower().strip() for cell in grid[0]]
            
            # Look for financial column indicators
            ticker_col = self._find_column_index(headers, ["symbol", "ticker", "stock", "security"])
            shares_col = self._find_column_index(headers, ["shares", "quantity", "qty", "units"])
            value_col = self._find_column_index(headers, ["value", "market value", "current value", "amount"])
            cost_col = self._find_column_index(headers, ["cost", "cost basis", "purchase price", "basis"])
            account_col = self._find_column_index(headers, ["account", "account number", "acct"])
            
            # Extract data rows
            for row_idx in range(1, rows):
                row = grid[row_idx]
                
                # Check if this looks like a data row
                if any(cell.strip() for cell in row):
                    record = {
                        "type": "holding",
                        "source": "azure_table",
                        "confidence": table.get("confidence", 0.0)
                    }
                    
                    if ticker_col is not None and ticker_col < len(row):
                        record["ticker_symbol"] = row[ticker_col].strip()
                    
                    if shares_col is not None and shares_col < len(row):
                        record["shares_quantity"] = row[shares_col].strip()
                    
                    if value_col is not None and value_col < len(row):
                        record["current_value"] = row[value_col].strip()
                    
                    if cost_col is not None and cost_col < len(row):
                        record["cost_basis"] = row[cost_col].strip()
                    
                    if account_col is not None and account_col < len(row):
                        record["account_number"] = row[account_col].strip()
                    
                    # Only add if we have at least ticker or shares
                    if record.get("ticker_symbol") or record.get("shares_quantity"):
                        financial_records.append(record)
        
        return financial_records
    
    def _find_column_index(self, headers: List[str], keywords: List[str]) -> Optional[int]:
        """Find column index that matches any of the keywords"""
        for i, header in enumerate(headers):
            for keyword in keywords:
                if keyword in header:
                    return i
        return None
