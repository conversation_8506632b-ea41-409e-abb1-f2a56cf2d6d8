import openai
import json
from typing import Dict, List, Any, Optional
from config.settings import settings
from models.data_models import ExtractionResult

class VisionExtractor:
    """Handles GPT-4V integration for document analysis"""
    
    def __init__(self):
        self.client = openai.OpenAI(api_key=settings.OPENAI_API_KEY)
        self.model = settings.OPENAI_MODEL
    
    def extract_portfolio_data(self, chunk_id: str, image_base64: str, text_content: str = "") -> ExtractionResult:
        """
        Extract portfolio data from document chunk using GPT-4V
        """
        try:
            # Prepare the prompt for portfolio data extraction
            system_prompt = self._get_extraction_prompt()
            
            messages = [
                {
                    "role": "system",
                    "content": system_prompt
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": f"Please analyze this document chunk and extract portfolio data. Text content (if available): {text_content[:1000]}..."
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{image_base64}",
                                "detail": "high"
                            }
                        }
                    ]
                }
            ]
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=2000,
                temperature=0.1
            )
            
            # Parse the response
            response_text = response.choices[0].message.content
            extracted_data = self._parse_extraction_response(response_text)
            
            return ExtractionResult(
                chunk_id=chunk_id,
                extracted_data=extracted_data,
                confidence_score=0.8,  # TODO: Implement confidence scoring
                errors=[],
                warnings=[]
            )
            
        except Exception as e:
            return ExtractionResult(
                chunk_id=chunk_id,
                extracted_data=[],
                confidence_score=0.0,
                errors=[f"Extraction failed: {str(e)}"],
                warnings=[]
            )
    
    def _get_extraction_prompt(self) -> str:
        """Get the system prompt for data extraction"""
        return """You are an expert financial document analyzer. Your task is to extract portfolio and investment data from documents.

IMPORTANT: Look for tabular data containing investment/portfolio information. The documents may have different formats and field names, but you need to intelligently map them to standard fields.

Extract the following information:
1. Document metadata:
   - Document name/title
   - Advisor name
   - Client name  
   - Portfolio ID
   - Total account value
   - Date of analysis or "as of" date

2. Tabular investment data (MOST IMPORTANT):
   - Account number
   - Ticker/Symbol (REQUIRED - variations: Symbol, Ticker, Stock Symbol, Security)
   - Shares/Quantity (REQUIRED - variations: Shares, Qty, Quantity, Units)
   - Current Value/Market Value (variations: Market Value, Current Value, Value)
   - Cost/Cost Basis (variations: Cost Basis, Original Cost, Purchase Price)

FIELD MAPPING INTELLIGENCE:
- "Symbol" = "Ticker" = "Stock Symbol" = "Security Symbol" → ticker_symbol
- "Shares" = "Qty" = "Quantity" = "Units" = "Share Count" → shares_quantity
- "Market Value" = "Current Value" = "Value" = "Market Val" → current_value
- "Cost Basis" = "Cost" = "Purchase Price" = "Original Cost" → cost_basis

Return the data in this exact JSON format:
{
  "document_metadata": {
    "document_name": "extracted_name",
    "advisor_name": "extracted_advisor",
    "client_name": "extracted_client",
    "portfolio_id": "extracted_id",
    "total_account_value": "extracted_value",
    "analysis_date": "extracted_date"
  },
  "tabular_data": [
    {
      "account_number": "account_if_found",
      "ticker_symbol": "REQUIRED_FIELD",
      "shares_quantity": "REQUIRED_FIELD", 
      "current_value": "value_if_found",
      "cost_basis": "cost_if_found"
    }
  ]
}

If no tabular data is found, return empty tabular_data array.
If required fields (ticker_symbol, shares_quantity) are missing, still include the row but mark them as "NOT_FOUND".
Be flexible with number formats, currencies, and text variations."""

    def _parse_extraction_response(self, response_text: str) -> List[Dict[str, Any]]:
        """Parse the GPT-4V response and extract structured data"""
        try:
            # Try to find JSON in the response
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = response_text[start_idx:end_idx]
                parsed_data = json.loads(json_str)
                
                # Extract and format the data
                result = []
                
                # Add document metadata as a special entry
                if "document_metadata" in parsed_data:
                    metadata = parsed_data["document_metadata"]
                    if any(metadata.values()):  # If any metadata found
                        result.append({
                            "type": "metadata",
                            **metadata
                        })
                
                # Add tabular data
                if "tabular_data" in parsed_data:
                    for item in parsed_data["tabular_data"]:
                        if item.get("ticker_symbol") and item.get("shares_quantity"):
                            result.append({
                                "type": "holding",
                                **item
                            })
                
                return result
            else:
                return []
                
        except json.JSONDecodeError:
            # If JSON parsing fails, try to extract data using text parsing
            return self._fallback_text_parsing(response_text)
        except Exception:
            return []
    
    def _fallback_text_parsing(self, text: str) -> List[Dict[str, Any]]:
        """Fallback method to extract data when JSON parsing fails"""
        # Simple text-based extraction as fallback
        # This is a basic implementation - can be enhanced
        result = []
        
        lines = text.split('\n')
        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['ticker', 'symbol', 'shares', 'quantity']):
                # Try to extract ticker and shares from the line
                words = line.split()
                ticker = None
                shares = None
                
                for i, word in enumerate(words):
                    # Look for ticker-like patterns (3-5 uppercase letters)
                    if word.isupper() and 2 <= len(word) <= 5:
                        ticker = word
                    # Look for number patterns for shares
                    elif word.replace(',', '').replace('.', '').isdigit():
                        shares = word
                
                if ticker and shares:
                    result.append({
                        "type": "holding",
                        "ticker_symbol": ticker,
                        "shares_quantity": shares
                    })
        
        return result
