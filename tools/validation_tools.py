from typing import List, Dict, Any
from models.data_models import ValidationResult, PortfolioData, AccountData
from config.settings import settings

class DataValidator:
    """Handles validation of extracted portfolio data"""
    
    def __init__(self):
        self.required_fields = settings.REQUIRED_FIELDS
        self.optional_fields = settings.OPTIONAL_FIELDS
    
    def validate_extracted_data(self, extracted_data: List[Dict[str, Any]]) -> ValidationResult:
        """
        Validate the extracted data and return validation results
        """
        errors = []
        warnings = []
        missing_required = []
        
        # Separate metadata and holdings
        metadata = {}
        holdings = []
        
        for item in extracted_data:
            if item.get("type") == "metadata":
                metadata.update({k: v for k, v in item.items() if k != "type"})
            elif item.get("type") == "holding":
                holdings.append({k: v for k, v in item.items() if k != "type"})
        
        # Validate holdings
        valid_holdings = []
        for holding in holdings:
            holding_errors = self._validate_holding(holding)
            if holding_errors:
                errors.extend(holding_errors)
            else:
                valid_holdings.append(holding)
        
        # Check if we have any valid holdings
        if not valid_holdings:
            errors.append("No valid holdings found with required fields (ticker_symbol, shares_quantity)")
            missing_required.extend(self.required_fields)
        
        # Create portfolio data object
        portfolio_data = None
        if valid_holdings or metadata:
            try:
                # Convert holdings to AccountData objects
                account_data_list = []
                for holding in valid_holdings:
                    account_data = AccountData(
                        account_number=holding.get("account_number"),
                        ticker_symbol=holding.get("ticker_symbol", ""),
                        shares_quantity=holding.get("shares_quantity", ""),
                        current_value=holding.get("current_value"),
                        cost_basis=holding.get("cost_basis")
                    )
                    account_data_list.append(account_data)
                
                portfolio_data = PortfolioData(
                    document_name=metadata.get("document_name"),
                    advisor_name=metadata.get("advisor_name"),
                    client_name=metadata.get("client_name"),
                    portfolio_id=metadata.get("portfolio_id"),
                    total_account_value=metadata.get("total_account_value"),
                    analysis_date=metadata.get("analysis_date"),
                    tabular_data=account_data_list
                )
                
            except Exception as e:
                errors.append(f"Failed to create portfolio data object: {str(e)}")
        
        # Add warnings for missing optional data
        if not metadata.get("document_name"):
            warnings.append("Document name not found")
        if not metadata.get("advisor_name"):
            warnings.append("Advisor name not found")
        if not metadata.get("client_name"):
            warnings.append("Client name not found")
        
        # Determine if validation passed
        is_valid = len(errors) == 0 and len(missing_required) == 0
        
        return ValidationResult(
            is_valid=is_valid,
            missing_required_fields=missing_required,
            validation_errors=errors,
            validation_warnings=warnings,
            processed_data=portfolio_data
        )
    
    def _validate_holding(self, holding: Dict[str, Any]) -> List[str]:
        """Validate a single holding record"""
        errors = []
        
        # Check required fields
        ticker = holding.get("ticker_symbol", "").strip()
        shares = holding.get("shares_quantity", "").strip()
        
        if not ticker or ticker == "NOT_FOUND":
            errors.append(f"Missing required field: ticker_symbol")
        
        if not shares or shares == "NOT_FOUND":
            errors.append(f"Missing required field: shares_quantity")
        
        # Validate ticker format (basic validation)
        if ticker and not self._is_valid_ticker(ticker):
            errors.append(f"Invalid ticker format: {ticker}")
        
        # Validate shares format (basic validation)
        if shares and not self._is_valid_quantity(shares):
            errors.append(f"Invalid shares quantity format: {shares}")
        
        return errors
    
    def _is_valid_ticker(self, ticker: str) -> bool:
        """Basic ticker validation"""
        if not ticker:
            return False
        
        # Remove common prefixes/suffixes and clean
        cleaned = ticker.upper().strip()
        
        # Basic rules: 1-5 characters, mostly letters
        if 1 <= len(cleaned) <= 6 and cleaned.replace('.', '').isalnum():
            return True
        
        return False
    
    def _is_valid_quantity(self, quantity: str) -> bool:
        """Basic quantity validation"""
        if not quantity:
            return False
        
        # Remove common formatting
        cleaned = quantity.replace(',', '').replace('$', '').strip()
        
        try:
            float(cleaned)
            return True
        except ValueError:
            return False
    
    def format_for_display(self, portfolio_data: PortfolioData) -> Dict[str, Any]:
        """Format portfolio data for display in Streamlit"""
        if not portfolio_data:
            return {}
        
        # Format metadata
        metadata = {
            "Document Name": portfolio_data.document_name or "Not found",
            "Advisor Name": portfolio_data.advisor_name or "Not found",
            "Client Name": portfolio_data.client_name or "Not found",
            "Portfolio ID": portfolio_data.portfolio_id or "Not found",
            "Total Account Value": portfolio_data.total_account_value or "Not found",
            "Analysis Date": portfolio_data.analysis_date or "Not found"
        }
        
        # Format tabular data
        table_data = []
        for holding in portfolio_data.tabular_data:
            table_data.append({
                "Account Number": holding.account_number or "N/A",
                "Ticker/Symbol": holding.ticker_symbol,
                "Shares/Quantity": holding.shares_quantity,
                "Current Value": holding.current_value or "N/A",
                "Cost Basis": holding.cost_basis or "N/A"
            })
        
        return {
            "metadata": metadata,
            "holdings": table_data
        }
