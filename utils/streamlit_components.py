import streamlit as st
import pandas as pd
import json
from typing import Dict, Any, List
from models.data_models import ValidationResult

class StreamlitComponents:
    """Custom Streamlit components for the application"""
    
    @staticmethod
    def display_processing_status(status_dict: Dict[str, str]):
        """Display processing status for all agents"""
        st.subheader("🤖 Agent Processing Status")
        
        # Create columns for status display
        cols = st.columns(5)
        
        agent_names = [
            "Chunking", "Extraction", "Aggregation", "Validation", "JSON Format"
        ]
        
        for i, (agent, status_text) in enumerate(status_dict.items()):
            with cols[i]:
                st.metric(
                    label=agent_names[i],
                    value=status_text.split()[-1],  # Get status without emoji
                    delta=None
                )
    
    @staticmethod
    def display_metadata_table(metadata: Dict[str, Any]):
        """Display document metadata in a nice table"""
        if not metadata:
            st.info("No document metadata found")
            return
        
        st.subheader("📋 Document Information")
        
        # Create a DataFrame for better display
        metadata_df = pd.DataFrame([
            {"Field": key, "Value": value or "Not found"}
            for key, value in metadata.items()
        ])
        
        st.dataframe(
            metadata_df,
            use_container_width=True,
            hide_index=True
        )
    
    @staticmethod
    def display_holdings_table(holdings: List[Dict[str, Any]]):
        """Display holdings data in a table"""
        if not holdings:
            st.warning("No portfolio holdings found")
            return
        
        st.subheader("💼 Portfolio Holdings")
        
        # Create DataFrame
        holdings_df = pd.DataFrame(holdings)
        
        # Format the table for better display
        st.dataframe(
            holdings_df,
            use_container_width=True,
            hide_index=True
        )
        
        # Add summary statistics
        if len(holdings) > 0:
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric("Total Holdings", len(holdings))
            
            with col2:
                unique_tickers = len(set(h.get("Ticker/Symbol", "") for h in holdings))
                st.metric("Unique Tickers", unique_tickers)
            
            with col3:
                # Try to calculate total value if available
                total_value = 0
                value_count = 0
                for holding in holdings:
                    value_str = holding.get("Current Value", "")
                    if value_str and value_str != "N/A":
                        try:
                            # Clean and convert value
                            clean_value = value_str.replace("$", "").replace(",", "").strip()
                            total_value += float(clean_value)
                            value_count += 1
                        except:
                            pass
                
                if value_count > 0:
                    st.metric("Total Value", f"${total_value:,.2f}")
                else:
                    st.metric("Total Value", "Not available")
    
    @staticmethod
    def display_validation_status(validation_result: ValidationResult):
        """Display validation status and errors/warnings"""
        st.subheader("✅ Validation Results")
        
        # Overall status
        if validation_result.is_valid:
            st.success("✅ Validation passed! All required fields found.")
        else:
            st.error("❌ Validation failed! Missing required fields.")
        
        # Display errors if any
        if validation_result.validation_errors:
            st.error("**Validation Errors:**")
            for error in validation_result.validation_errors:
                st.error(f"• {error}")
        
        # Display warnings if any
        if validation_result.validation_warnings:
            st.warning("**Validation Warnings:**")
            for warning in validation_result.validation_warnings:
                st.warning(f"• {warning}")
        
        # Display missing required fields
        if validation_result.missing_required_fields:
            st.error("**Missing Required Fields:**")
            for field in validation_result.missing_required_fields:
                st.error(f"• {field}")
    
    @staticmethod
    def display_json_output(json_string: str):
        """Display JSON output with syntax highlighting"""
        st.subheader("📄 JSON Output")
        
        try:
            # Parse and reformat JSON for better display
            parsed_json = json.loads(json_string)
            formatted_json = json.dumps(parsed_json, indent=2, ensure_ascii=False)
            
            # Display in code block
            st.code(formatted_json, language='json')
            
            # Add download button
            st.download_button(
                label="📥 Download JSON",
                data=formatted_json,
                file_name="extracted_portfolio_data.json",
                mime="application/json"
            )
            
        except json.JSONDecodeError:
            st.error("Invalid JSON format")
            st.code(json_string, language='text')
    
    @staticmethod
    def display_file_info(file_info: Dict[str, Any]):
        """Display file information"""
        st.subheader("📁 File Information")
        
        # Create columns for file info
        cols = st.columns(len(file_info))
        
        for i, (key, value) in enumerate(file_info.items()):
            with cols[i]:
                st.metric(label=key, value=value)
    
    @staticmethod
    def create_sidebar_info():
        """Create sidebar with application information"""
        with st.sidebar:
            st.title("📄 Document Extractor")
            st.markdown("---")
            
            st.subheader("🤖 Agent Workflow")
            st.markdown("""
            **Agent 1:** Document Chunking
            - Splits document into 5-page chunks
            - Converts to images for vision processing
            
            **Agent 2:** Data Extraction  
            - Uses GPT-4V for layout detection
            - Extracts portfolio data intelligently
            
            **Agent 3:** Data Aggregation
            - Combines data from all chunks
            - Resolves conflicts and duplicates
            
            **Agent 4:** Data Validation
            - Validates required fields
            - Prepares data for display
            
            **Agent 5:** JSON Formatting
            - Creates final structured output
            - Handles missing data gracefully
            """)
            
            st.markdown("---")
            st.subheader("📋 Required Fields")
            st.markdown("""
            - **Ticker/Symbol** (Required)
            - **Shares/Quantity** (Required)
            - Account Number (Optional)
            - Current Value (Optional)
            - Cost Basis (Optional)
            """)
            
            st.markdown("---")
            st.subheader("📄 Supported Formats")
            st.markdown("""
            - PDF documents
            - DOCX documents  
            - PNG/JPG images
            - Max size: 50MB
            """)
    
    @staticmethod
    def display_error_message(error_message: str):
        """Display error message with styling"""
        st.error(f"❌ **Error:** {error_message}")
    
    @staticmethod
    def display_success_message(message: str):
        """Display success message with styling"""
        st.success(f"✅ **Success:** {message}")
    
    @staticmethod
    def display_info_message(message: str):
        """Display info message with styling"""
        st.info(f"ℹ️ **Info:** {message}")
    
    @staticmethod
    def create_progress_bar(progress: float, text: str = ""):
        """Create a progress bar"""
        progress_bar = st.progress(progress)
        if text:
            st.text(text)
        return progress_bar
