from crewai import Crew, Process
from agents.chunking_agent import ChunkingAgent
from agents.extraction_agent import ExtractionAgent
from agents.aggregation_agent import AggregationAgent
from agents.validation_agent import ValidationAgent
from agents.json_formatter_agent import JsonFormatterAgent
from models.data_models import ValidationR<PERSON>ult
from typing import <PERSON><PERSON>, Dict, Any
import time

class CrewOrchestrator:
    """Orchestrates the multi-agent document processing workflow"""
    
    def __init__(self):
        # Initialize all agents
        self.chunking_agent = ChunkingAgent()
        self.extraction_agent = ExtractionAgent()
        self.aggregation_agent = AggregationAgent()
        self.validation_agent = ValidationAgent()
        self.json_formatter_agent = JsonFormatterAgent()
        
        # Processing status
        self.processing_status = {
            "chunking": "pending",
            "extraction": "pending", 
            "aggregation": "pending",
            "validation": "pending",
            "formatting": "pending"
        }
    
    def process_document(self, file_content: bytes, filename: str) -> Tuple[ValidationResult, str, Dict[str, str]]:
        """
        Process document through the complete agent workflow
        
        Returns:
            - ValidationResult: Validation results with processed data
            - str: Final JSON output
            - Dict[str, str]: Processing status for each agent
        """
        try:
            # Step 1: Document Chunking
            self.processing_status["chunking"] = "running"
            print("🔄 Agent 1: Starting document chunking...")
            
            chunks = self.chunking_agent.process_document(file_content, filename)
            
            if not chunks:
                self.processing_status["chunking"] = "failed"
                return self._create_error_result("Document chunking failed"), "{}", self.processing_status
            
            self.processing_status["chunking"] = "completed"
            print(f"✅ Agent 1: Created {len(chunks)} chunks")
            
            # Step 2: Data Extraction
            self.processing_status["extraction"] = "running"
            print("🔄 Agent 2: Starting data extraction with GPT-4V...")
            
            extraction_results = self.extraction_agent.extract_from_chunks(chunks)
            
            if not extraction_results:
                self.processing_status["extraction"] = "failed"
                return self._create_error_result("Data extraction failed"), "{}", self.processing_status
            
            self.processing_status["extraction"] = "completed"
            print(f"✅ Agent 2: Processed {len(extraction_results)} chunks")
            
            # Step 3: Data Aggregation
            self.processing_status["aggregation"] = "running"
            print("🔄 Agent 3: Aggregating extracted data...")
            
            aggregated_data = self.aggregation_agent.aggregate_results(extraction_results)
            
            if not aggregated_data:
                self.processing_status["aggregation"] = "failed"
                return self._create_error_result("Data aggregation failed"), "{}", self.processing_status
            
            self.processing_status["aggregation"] = "completed"
            print("✅ Agent 3: Data aggregation completed")
            
            # Step 4: Data Validation
            self.processing_status["validation"] = "running"
            print("🔄 Agent 4: Validating extracted data...")
            
            validation_result = self.validation_agent.validate_data(aggregated_data)
            
            self.processing_status["validation"] = "completed"
            print(f"✅ Agent 4: Validation completed (Valid: {validation_result.is_valid})")
            
            # Step 5: JSON Formatting
            self.processing_status["formatting"] = "running"
            print("🔄 Agent 5: Creating JSON output...")
            
            json_output = self.json_formatter_agent.format_to_json(validation_result)
            
            self.processing_status["formatting"] = "completed"
            print("✅ Agent 5: JSON formatting completed")
            
            return validation_result, json_output, self.processing_status
            
        except Exception as e:
            print(f"❌ Error in crew orchestrator: {str(e)}")
            # Mark current step as failed
            for step, status in self.processing_status.items():
                if status == "running":
                    self.processing_status[step] = "failed"
                    break
            
            return self._create_error_result(f"Processing failed: {str(e)}"), "{}", self.processing_status
    
    def _create_error_result(self, error_message: str) -> ValidationResult:
        """Create an error validation result"""
        return ValidationResult(
            is_valid=False,
            missing_required_fields=["ticker_symbol", "shares_quantity"],
            validation_errors=[error_message],
            validation_warnings=[],
            processed_data=None
        )
    
    def get_processing_status(self) -> Dict[str, str]:
        """Get current processing status"""
        return self.processing_status.copy()
    
    def reset_status(self):
        """Reset processing status for new document"""
        self.processing_status = {
            "chunking": "pending",
            "extraction": "pending",
            "aggregation": "pending", 
            "validation": "pending",
            "formatting": "pending"
        }
    
    def get_status_emoji(self, status: str) -> str:
        """Get emoji for status"""
        emoji_map = {
            "pending": "⏸️",
            "running": "🔄",
            "completed": "✅",
            "failed": "❌"
        }
        return emoji_map.get(status, "❓")
    
    def get_status_display(self) -> Dict[str, str]:
        """Get status with emojis for display"""
        return {
            f"Agent 1 (Chunking)": f"{self.get_status_emoji(self.processing_status['chunking'])} {self.processing_status['chunking'].title()}",
            f"Agent 2 (Extraction)": f"{self.get_status_emoji(self.processing_status['extraction'])} {self.processing_status['extraction'].title()}",
            f"Agent 3 (Aggregation)": f"{self.get_status_emoji(self.processing_status['aggregation'])} {self.processing_status['aggregation'].title()}",
            f"Agent 4 (Validation)": f"{self.get_status_emoji(self.processing_status['validation'])} {self.processing_status['validation'].title()}",
            f"Agent 5 (JSON Format)": f"{self.get_status_emoji(self.processing_status['formatting'])} {self.processing_status['formatting'].title()}"
        }
