import streamlit as st
import base64
from typing import Optional, <PERSON><PERSON>
from config.settings import settings
import fitz  # pymupdf
from PIL import Image
import io

class FileHandler:
    """Handles file upload and preview functionality"""
    
    def __init__(self):
        self.max_file_size = settings.MAX_FILE_SIZE
        self.supported_formats = settings.SUPPORTED_FORMATS
    
    def handle_file_upload(self) -> Tuple[Optional[bytes], Optional[str]]:
        """Handle file upload with validation"""
        uploaded_file = st.file_uploader(
            "Upload your document",
            type=['pdf', 'docx', 'png', 'jpg', 'jpeg'],
            help=f"Supported formats: {', '.join(self.supported_formats)}. Max size: {self.max_file_size // (1024*1024)}MB"
        )
        
        if uploaded_file is not None:
            # Validate file size
            if uploaded_file.size > self.max_file_size:
                st.error(f"File size ({uploaded_file.size // (1024*1024)}MB) exceeds maximum allowed size ({self.max_file_size // (1024*1024)}MB)")
                return None, None
            
            # Validate file format
            file_extension = f".{uploaded_file.name.split('.')[-1].lower()}"
            if file_extension not in self.supported_formats:
                st.error(f"Unsupported file format: {file_extension}")
                return None, None
            
            # Read file content
            file_content = uploaded_file.read()
            return file_content, uploaded_file.name
        
        return None, None
    
    def create_document_preview(self, file_content: bytes, filename: str) -> str:
        """Create document preview for display"""
        try:
            file_extension = filename.lower().split('.')[-1]
            
            if file_extension == 'pdf':
                return self._create_pdf_preview(file_content)
            elif file_extension in ['png', 'jpg', 'jpeg']:
                return self._create_image_preview(file_content)
            elif file_extension == 'docx':
                return self._create_docx_preview()
            else:
                return "Preview not available for this file type"
                
        except Exception as e:
            return f"Error creating preview: {str(e)}"
    
    def _create_pdf_preview(self, file_content: bytes) -> str:
        """Create PDF preview"""
        try:
            pdf_document = fitz.open(stream=file_content, filetype="pdf")
            
            # Get first page as image
            first_page = pdf_document[0]
            pix = first_page.get_pixmap(matrix=fitz.Matrix(1.5, 1.5))  # 1.5x zoom
            img_data = pix.tobytes("png")
            
            # Convert to base64 for display
            img_base64 = base64.b64encode(img_data).decode()
            
            pdf_document.close()
            
            return f"data:image/png;base64,{img_base64}"
            
        except Exception as e:
            return f"Error creating PDF preview: {str(e)}"
    
    def _create_image_preview(self, file_content: bytes) -> str:
        """Create image preview"""
        try:
            # Convert to base64 for display
            img_base64 = base64.b64encode(file_content).decode()
            
            # Determine image format
            img = Image.open(io.BytesIO(file_content))
            format_str = img.format.lower()
            
            return f"data:image/{format_str};base64,{img_base64}"
            
        except Exception as e:
            return f"Error creating image preview: {str(e)}"
    
    def _create_docx_preview(self) -> str:
        """Create DOCX preview placeholder"""
        return "📄 DOCX Document - Preview not available. Processing will extract text content."
    
    def display_document_preview(self, preview_data: str, filename: str):
        """Display document preview in Streamlit"""
        st.subheader("📄 Document Preview")
        
        if preview_data.startswith("data:image"):
            # Display image
            st.image(preview_data, caption=filename, use_column_width=True)
        elif preview_data.startswith("📄"):
            # Display text placeholder
            st.info(preview_data)
        else:
            # Display error message
            st.error(preview_data)
    
    def get_file_info(self, file_content: bytes, filename: str) -> dict:
        """Get file information for display"""
        file_size_mb = len(file_content) / (1024 * 1024)
        file_extension = filename.split('.')[-1].upper()
        
        info = {
            "Filename": filename,
            "Size": f"{file_size_mb:.2f} MB",
            "Format": file_extension,
            "Status": "✅ Ready for processing"
        }
        
        # Add specific info based on file type
        if file_extension.lower() == 'pdf':
            try:
                pdf_document = fitz.open(stream=file_content, filetype="pdf")
                info["Pages"] = len(pdf_document)
                info["Estimated Chunks"] = (len(pdf_document) + settings.CHUNK_SIZE - 1) // settings.CHUNK_SIZE
                pdf_document.close()
            except:
                info["Pages"] = "Unknown"
                info["Estimated Chunks"] = "Unknown"
        
        return info
