#!/usr/bin/env python3
"""
Test script to verify the installation and basic functionality
"""

import sys
import os

def test_imports():
    """Test if all required packages can be imported"""
    print("Testing imports...")
    
    try:
        import streamlit
        print("✅ Streamlit imported successfully")
    except ImportError as e:
        print(f"❌ Streamlit import failed: {e}")
        return False
    
    try:
        import crewai
        print("✅ CrewAI imported successfully")
    except ImportError as e:
        print(f"❌ CrewAI import failed: {e}")
        return False
    
    try:
        import openai
        print("✅ OpenAI imported successfully")
    except ImportError as e:
        print(f"❌ OpenAI import failed: {e}")
        return False
    
    try:
        import PyPDF2
        print("✅ PyPDF2 imported successfully")
    except ImportError as e:
        print(f"❌ PyPDF2 import failed: {e}")
        return False
    
    try:
        import fitz  # pymupdf
        print("✅ PyMuPDF imported successfully")
    except ImportError as e:
        print(f"❌ PyMuPDF import failed: {e}")
        return False
    
    try:
        from docx import Document
        print("✅ python-docx imported successfully")
    except ImportError as e:
        print(f"❌ python-docx import failed: {e}")
        return False
    
    try:
        from PIL import Image
        print("✅ Pillow imported successfully")
    except ImportError as e:
        print(f"❌ Pillow import failed: {e}")
        return False
    
    try:
        import pydantic
        print("✅ Pydantic imported successfully")
    except ImportError as e:
        print(f"❌ Pydantic import failed: {e}")
        return False
    
    try:
        import pandas
        print("✅ Pandas imported successfully")
    except ImportError as e:
        print(f"❌ Pandas import failed: {e}")
        return False
    
    return True

def test_project_structure():
    """Test if all required files and directories exist"""
    print("\nTesting project structure...")
    
    required_files = [
        "app.py",
        "requirements.txt",
        "README.md",
        ".env.example",
        "config/settings.py",
        "models/data_models.py",
        "tools/document_processor.py",
        "tools/vision_tools.py",
        "tools/validation_tools.py",
        "agents/chunking_agent.py",
        "agents/extraction_agent.py",
        "agents/aggregation_agent.py",
        "agents/validation_agent.py",
        "agents/json_formatter_agent.py",
        "utils/crew_orchestrator.py",
        "utils/file_handlers.py",
        "utils/streamlit_components.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - MISSING")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def test_environment():
    """Test environment configuration"""
    print("\nTesting environment...")
    
    # Check if .env file exists
    if os.path.exists(".env"):
        print("✅ .env file found")
        
        # Try to load environment variables
        try:
            from dotenv import load_dotenv
            load_dotenv()
            
            api_key = os.getenv("OPENAI_API_KEY")
            if api_key:
                print("✅ OPENAI_API_KEY is set")
            else:
                print("⚠️ OPENAI_API_KEY is not set in .env file")
                
        except ImportError:
            print("⚠️ python-dotenv not installed, but .env file exists")
    else:
        print("⚠️ .env file not found (copy from .env.example and add your API key)")

def test_basic_functionality():
    """Test basic functionality of core components"""
    print("\nTesting basic functionality...")
    
    try:
        # Test data models
        from models.data_models import PortfolioData, AccountData
        
        # Create test data
        account = AccountData(
            ticker_symbol="TEST",
            shares_quantity="100"
        )
        
        portfolio = PortfolioData(
            document_name="test.pdf",
            tabular_data=[account]
        )
        
        print("✅ Data models working correctly")
        
    except Exception as e:
        print(f"❌ Data models test failed: {e}")
        return False
    
    try:
        # Test document processor
        from tools.document_processor import DocumentProcessor
        processor = DocumentProcessor()
        print("✅ Document processor initialized successfully")
        
    except Exception as e:
        print(f"❌ Document processor test failed: {e}")
        return False
    
    try:
        # Test validation tools
        from tools.validation_tools import DataValidator
        validator = DataValidator()
        print("✅ Data validator initialized successfully")
        
    except Exception as e:
        print(f"❌ Data validator test failed: {e}")
        return False
    
    return True

def main():
    """Run all tests"""
    print("🧪 Running installation tests...\n")
    
    tests_passed = 0
    total_tests = 4
    
    # Test imports
    if test_imports():
        tests_passed += 1
    
    # Test project structure
    if test_project_structure():
        tests_passed += 1
    
    # Test environment
    test_environment()  # This is informational, not pass/fail
    tests_passed += 1
    
    # Test basic functionality
    if test_basic_functionality():
        tests_passed += 1
    
    print(f"\n📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! The application should work correctly.")
        print("\nTo run the application:")
        print("1. Make sure you have set OPENAI_API_KEY in your .env file")
        print("2. Run: streamlit run app.py")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
