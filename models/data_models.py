from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

class AccountData(BaseModel):
    """Model for individual account/holding data"""
    account_number: Optional[str] = None
    ticker_symbol: str = Field(..., description="Stock ticker/symbol (required)")
    shares_quantity: str = Field(..., description="Number of shares/quantity (required)")
    current_value: Optional[str] = None
    cost_basis: Optional[str] = None
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class PortfolioData(BaseModel):
    """Main model for portfolio document data"""
    document_name: Optional[str] = None
    advisor_name: Optional[str] = None
    client_name: Optional[str] = None
    portfolio_id: Optional[str] = None
    total_account_value: Optional[str] = None
    analysis_date: Optional[str] = None
    tabular_data: List[AccountData] = Field(default_factory=list)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class DocumentChunk(BaseModel):
    """Model for document chunks"""
    chunk_id: str
    page_start: int
    page_end: int
    content: str
    image_data: Optional[str] = None  # Base64 encoded image
    metadata: Dict[str, Any] = Field(default_factory=dict)

class ExtractionResult(BaseModel):
    """Model for extraction results from each chunk"""
    chunk_id: str
    extracted_data: List[Dict[str, Any]] = Field(default_factory=list)
    confidence_score: Optional[float] = None
    errors: List[str] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)

class ValidationResult(BaseModel):
    """Model for validation results"""
    is_valid: bool
    missing_required_fields: List[str] = Field(default_factory=list)
    validation_errors: List[str] = Field(default_factory=list)
    validation_warnings: List[str] = Field(default_factory=list)
    processed_data: Optional[PortfolioData] = None
