from crewai import Agent, Task
from tools.document_processor import DocumentProcessor
from models.data_models import DocumentChunk
from typing import List

class ChunkingAgent:
    """Agent responsible for document chunking"""
    
    def __init__(self):
        self.processor = DocumentProcessor()
        self.agent = Agent(
            role="Document Preprocessor",
            goal="Split uploaded documents into manageable chunks for processing",
            backstory="""You are an expert document processor who specializes in breaking down 
            large documents into smaller, manageable chunks while preserving the document structure 
            and maintaining important metadata. You handle various document formats including PDF, 
            DOCX, and images.""",
            verbose=True,
            allow_delegation=False
        )
    
    def create_chunking_task(self, file_content: bytes, filename: str) -> Task:
        """Create a task for document chunking"""
        return Task(
            description=f"""
            Process the uploaded document '{filename}' and split it into chunks of {self.processor.chunk_size} pages each.
            
            Requirements:
            1. Detect the document format (PDF, DOCX, or image)
            2. Extract both text content and visual information
            3. Split into chunks of {self.processor.chunk_size} pages each
            4. Convert pages to images for vision processing
            5. Maintain page numbering and metadata
            6. Handle different document formats appropriately
            
            Input: Document file content and filename
            Output: List of DocumentChunk objects with text content, image data, and metadata
            """,
            agent=self.agent,
            expected_output="List of DocumentChunk objects ready for vision processing"
        )
    
    def process_document(self, file_content: bytes, filename: str) -> List[DocumentChunk]:
        """Process document and return chunks"""
        try:
            chunks = self.processor.process_document(file_content, filename)
            return chunks
        except Exception as e:
            print(f"Error in chunking agent: {str(e)}")
            return []
