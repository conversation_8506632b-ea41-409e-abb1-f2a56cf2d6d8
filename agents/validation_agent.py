from crewai import Agent, Task
from tools.validation_tools import DataValidator
from models.data_models import ValidationResult
from typing import List, Dict, Any

class ValidationAgent:
    """Agent responsible for data validation and table display preparation"""
    
    def __init__(self):
        self.validator = DataValidator()
        self.agent = Agent(
            role="Data Validator & Presenter",
            goal="Validate extracted data, ensure required fields are present, and prepare data for table display",
            backstory="""You are a meticulous data quality specialist who ensures the accuracy 
            and completeness of financial portfolio data. You excel at identifying missing 
            required information, validating data formats, and preparing clean datasets for 
            presentation. You understand the critical importance of data integrity in 
            financial reporting.""",
            verbose=True,
            allow_delegation=False
        )
    
    def create_validation_task(self, aggregated_data: List[Dict[str, Any]]) -> Task:
        """Create a task for data validation"""
        return Task(
            description=f"""
            Validate the aggregated portfolio data and prepare it for display.
            
            Requirements:
            1. Validate all required fields are present (ticker_symbol, shares_quantity)
            2. Check data format and consistency
            3. Identify missing or invalid data
            4. Prepare data for table display in Streamlit
            5. Generate validation report with errors and warnings
            6. Fail analysis only if required fields are missing
            
            Validation Rules:
            - ticker_symbol: Must be present and valid format
            - shares_quantity: Must be present and numeric
            - Other fields: Optional but validate format if present
            
            Data Quality Checks:
            - Remove duplicate entries
            - Validate ticker symbols (basic format check)
            - Validate numeric fields
            - Check for reasonable value ranges
            
            Input: Aggregated data from all chunks
            Output: ValidationResult with validated data and error/warning reports
            """,
            agent=self.agent,
            expected_output="ValidationResult object with validated portfolio data ready for display"
        )
    
    def validate_data(self, aggregated_data: List[Dict[str, Any]]) -> ValidationResult:
        """Validate the aggregated data"""
        try:
            return self.validator.validate_extracted_data(aggregated_data)
        except Exception as e:
            print(f"Error in validation agent: {str(e)}")
            return ValidationResult(
                is_valid=False,
                missing_required_fields=["ticker_symbol", "shares_quantity"],
                validation_errors=[f"Validation failed: {str(e)}"],
                validation_warnings=[],
                processed_data=None
            )
    
    def format_for_display(self, validation_result: ValidationResult) -> Dict[str, Any]:
        """Format validated data for Streamlit display"""
        try:
            if not validation_result.processed_data:
                return {
                    "metadata": {},
                    "holdings": [],
                    "validation_status": {
                        "is_valid": validation_result.is_valid,
                        "errors": validation_result.validation_errors,
                        "warnings": validation_result.validation_warnings,
                        "missing_fields": validation_result.missing_required_fields
                    }
                }
            
            display_data = self.validator.format_for_display(validation_result.processed_data)
            display_data["validation_status"] = {
                "is_valid": validation_result.is_valid,
                "errors": validation_result.validation_errors,
                "warnings": validation_result.validation_warnings,
                "missing_fields": validation_result.missing_required_fields
            }
            
            return display_data
            
        except Exception as e:
            print(f"Error formatting data for display: {str(e)}")
            return {
                "metadata": {},
                "holdings": [],
                "validation_status": {
                    "is_valid": False,
                    "errors": [f"Display formatting failed: {str(e)}"],
                    "warnings": [],
                    "missing_fields": []
                }
            }
