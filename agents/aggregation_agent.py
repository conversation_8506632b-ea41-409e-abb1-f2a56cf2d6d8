from crewai import Agent, Task
from models.data_models import ExtractionResult
from typing import List, Dict, Any

class AggregationAgent:
    """Agent responsible for aggregating and combining extracted data from all chunks"""
    
    def __init__(self):
        self.agent = Agent(
            role="Data Collector & Combiner",
            goal="Aggregate all extracted data from parallel chunk processing and resolve conflicts",
            backstory="""You are an expert data analyst who specializes in combining and 
            reconciling financial data from multiple sources. You excel at identifying 
            duplicate entries, resolving conflicts between data sources, and creating 
            unified datasets from fragmented information.""",
            verbose=True,
            allow_delegation=False
        )
    
    def create_aggregation_task(self, extraction_results: List[ExtractionResult]) -> Task:
        """Create a task for data aggregation"""
        return Task(
            description=f"""
            Aggregate and combine extracted data from {len(extraction_results)} chunk processing results.
            
            Requirements:
            1. Collect all extracted data from chunk processing results
            2. Identify and merge duplicate or overlapping entries
            3. Resolve conflicts between different chunks
            4. Combine metadata from all chunks
            5. Create a unified dataset
            6. Handle partial data and missing information
            7. Maintain data integrity and consistency
            
            Conflict Resolution Rules:
            - For metadata: Use the most complete information found
            - For holdings: Merge by ticker symbol, sum quantities if needed
            - For duplicates: Keep the entry with most complete data
            - For inconsistencies: Flag as warnings but don't fail
            
            Input: List of ExtractionResult objects from chunk processing
            Output: Single aggregated dataset with all portfolio information
            """,
            agent=self.agent,
            expected_output="Unified dataset containing all extracted portfolio data"
        )
    
    def aggregate_results(self, extraction_results: List[ExtractionResult]) -> List[Dict[str, Any]]:
        """Aggregate extraction results from all chunks"""
        try:
            aggregated_data = []
            metadata_entries = []
            holding_entries = []
            all_errors = []
            all_warnings = []
            
            # Collect all data and separate by type
            for result in extraction_results:
                # Collect errors and warnings
                all_errors.extend(result.errors)
                all_warnings.extend(result.warnings)
                
                # Process extracted data
                for item in result.extracted_data:
                    if item.get("type") == "metadata":
                        metadata_entries.append(item)
                    elif item.get("type") == "holding":
                        holding_entries.append(item)
            
            # Aggregate metadata
            merged_metadata = self._merge_metadata(metadata_entries)
            if merged_metadata:
                aggregated_data.append(merged_metadata)
            
            # Aggregate holdings
            merged_holdings = self._merge_holdings(holding_entries)
            aggregated_data.extend(merged_holdings)
            
            # Add summary information
            if all_errors or all_warnings:
                summary = {
                    "type": "processing_summary",
                    "total_chunks_processed": len(extraction_results),
                    "total_errors": len(all_errors),
                    "total_warnings": len(all_warnings),
                    "errors": all_errors,
                    "warnings": all_warnings
                }
                aggregated_data.append(summary)
            
            return aggregated_data
            
        except Exception as e:
            print(f"Error in aggregation agent: {str(e)}")
            return []
    
    def _merge_metadata(self, metadata_entries: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Merge metadata from multiple chunks"""
        if not metadata_entries:
            return None
        
        merged = {"type": "metadata"}
        
        # Fields to merge
        fields = [
            "document_name", "advisor_name", "client_name", 
            "portfolio_id", "total_account_value", "analysis_date"
        ]
        
        for field in fields:
            # Find the most complete value for each field
            values = [entry.get(field) for entry in metadata_entries if entry.get(field)]
            if values:
                # Use the longest/most complete value
                merged[field] = max(values, key=len) if isinstance(values[0], str) else values[0]
        
        return merged if any(merged.get(field) for field in fields) else None
    
    def _merge_holdings(self, holding_entries: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Merge holdings, handling duplicates and conflicts"""
        if not holding_entries:
            return []
        
        # Group by ticker symbol
        holdings_by_ticker = {}
        
        for holding in holding_entries:
            ticker = holding.get("ticker_symbol", "").upper().strip()
            if not ticker or ticker == "NOT_FOUND":
                continue
            
            if ticker not in holdings_by_ticker:
                holdings_by_ticker[ticker] = []
            holdings_by_ticker[ticker].append(holding)
        
        # Merge holdings for each ticker
        merged_holdings = []
        for ticker, holdings_list in holdings_by_ticker.items():
            merged_holding = self._merge_single_ticker_holdings(holdings_list)
            if merged_holding:
                merged_holdings.append(merged_holding)
        
        return merged_holdings
    
    def _merge_single_ticker_holdings(self, holdings_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Merge multiple holdings for the same ticker"""
        if not holdings_list:
            return None
        
        if len(holdings_list) == 1:
            return holdings_list[0]
        
        # Start with the first holding
        merged = holdings_list[0].copy()
        
        # Merge quantities (sum them if they're numeric)
        total_shares = 0
        shares_found = False
        
        for holding in holdings_list:
            shares_str = holding.get("shares_quantity", "")
            if shares_str and shares_str != "NOT_FOUND":
                try:
                    # Clean and convert to number
                    clean_shares = shares_str.replace(",", "").replace("$", "").strip()
                    shares_num = float(clean_shares)
                    total_shares += shares_num
                    shares_found = True
                except ValueError:
                    # If can't convert, keep the original string
                    if not merged.get("shares_quantity") or merged.get("shares_quantity") == "NOT_FOUND":
                        merged["shares_quantity"] = shares_str
        
        if shares_found:
            merged["shares_quantity"] = str(int(total_shares) if total_shares.is_integer() else total_shares)
        
        # For other fields, use the most complete value
        fields = ["account_number", "current_value", "cost_basis"]
        for field in fields:
            values = [h.get(field) for h in holdings_list if h.get(field) and h.get(field) != "NOT_FOUND"]
            if values:
                merged[field] = max(values, key=len) if isinstance(values[0], str) else values[0]
        
        return merged
