from crewai import Agent, Task
from models.data_models import ValidationResult, PortfolioData
import json
from typing import Dict, Any

class JsonFormatterAgent:
    """Agent responsible for creating final JSON output"""
    
    def __init__(self):
        self.agent = Agent(
            role="Output Formatter",
            goal="Create final JSON output in the specified format with all extracted portfolio data",
            backstory="""You are a data formatting specialist who creates clean, structured 
            JSON outputs for financial portfolio data. You ensure that all data is properly 
            formatted, follows the required schema, and handles missing or incomplete 
            information gracefully.""",
            verbose=True,
            allow_delegation=False
        )
    
    def create_formatting_task(self, validation_result: ValidationResult) -> Task:
        """Create a task for JSON formatting"""
        return Task(
            description="""
            Create the final JSON output in the specified format.
            
            Required JSON Structure:
            {
                "document_name": "extracted_name",
                "advisor_name": "extracted_advisor", 
                "client_name": "extracted_client",
                "portfolio_id": "extracted_id",
                "total_account_value": "extracted_value",
                "date_of_analysis": "extracted_date",
                "tabular_data": [
                    {
                        "account_number": "account_if_found",
                        "ticker_symbol": "REQUIRED_FIELD",
                        "shares_quantity": "REQUIRED_FIELD",
                        "current_value": "value_if_found", 
                        "cost_basis": "cost_if_found"
                    }
                ],
                "validation_status": {
                    "is_valid": true/false,
                    "missing_required_fields": [],
                    "errors": [],
                    "warnings": []
                }
            }
            
            Requirements:
            1. Include all extracted data in the specified format
            2. Handle missing fields gracefully (use null or "Not found")
            3. Ensure JSON is valid and properly formatted
            4. Include validation status and any errors/warnings
            5. Maintain data types and structure consistency
            
            Input: ValidationResult with processed portfolio data
            Output: Formatted JSON string ready for display
            """,
            agent=self.agent,
            expected_output="Properly formatted JSON string containing all portfolio data"
        )
    
    def format_to_json(self, validation_result: ValidationResult) -> str:
        """Format validation result to JSON string"""
        try:
            # Create the output structure
            output = {
                "document_name": None,
                "advisor_name": None,
                "client_name": None,
                "portfolio_id": None,
                "total_account_value": None,
                "date_of_analysis": None,
                "tabular_data": [],
                "validation_status": {
                    "is_valid": validation_result.is_valid,
                    "missing_required_fields": validation_result.missing_required_fields,
                    "errors": validation_result.validation_errors,
                    "warnings": validation_result.validation_warnings
                }
            }
            
            # Fill in data if available
            if validation_result.processed_data:
                portfolio_data = validation_result.processed_data
                
                # Fill metadata
                output["document_name"] = portfolio_data.document_name
                output["advisor_name"] = portfolio_data.advisor_name
                output["client_name"] = portfolio_data.client_name
                output["portfolio_id"] = portfolio_data.portfolio_id
                output["total_account_value"] = portfolio_data.total_account_value
                output["date_of_analysis"] = portfolio_data.analysis_date
                
                # Fill tabular data
                for holding in portfolio_data.tabular_data:
                    holding_dict = {
                        "account_number": holding.account_number,
                        "ticker_symbol": holding.ticker_symbol,
                        "shares_quantity": holding.shares_quantity,
                        "current_value": holding.current_value,
                        "cost_basis": holding.cost_basis
                    }
                    output["tabular_data"].append(holding_dict)
            
            # Convert to formatted JSON string
            return json.dumps(output, indent=2, ensure_ascii=False)
            
        except Exception as e:
            # Return error JSON if formatting fails
            error_output = {
                "document_name": None,
                "advisor_name": None,
                "client_name": None,
                "portfolio_id": None,
                "total_account_value": None,
                "date_of_analysis": None,
                "tabular_data": [],
                "validation_status": {
                    "is_valid": False,
                    "missing_required_fields": ["ticker_symbol", "shares_quantity"],
                    "errors": [f"JSON formatting failed: {str(e)}"],
                    "warnings": []
                }
            }
            return json.dumps(error_output, indent=2, ensure_ascii=False)
    
    def create_summary_json(self, portfolio_data: PortfolioData, processing_stats: Dict[str, Any] = None) -> str:
        """Create a summary JSON with additional processing statistics"""
        try:
            summary = {
                "processing_summary": {
                    "total_holdings_found": len(portfolio_data.tabular_data) if portfolio_data else 0,
                    "document_processed": portfolio_data.document_name if portfolio_data else "Unknown",
                    "extraction_timestamp": None,  # Could add timestamp
                    "processing_stats": processing_stats or {}
                },
                "data_quality": {
                    "has_required_fields": True,  # Will be updated based on validation
                    "completeness_score": 0.0,  # Could calculate based on filled fields
                    "confidence_score": 0.0  # Could aggregate from extraction results
                }
            }
            
            if portfolio_data:
                # Calculate completeness score
                total_fields = 6  # metadata fields
                filled_fields = sum(1 for field in [
                    portfolio_data.document_name,
                    portfolio_data.advisor_name,
                    portfolio_data.client_name,
                    portfolio_data.portfolio_id,
                    portfolio_data.total_account_value,
                    portfolio_data.analysis_date
                ] if field)
                
                summary["data_quality"]["completeness_score"] = filled_fields / total_fields
            
            return json.dumps(summary, indent=2, ensure_ascii=False)
            
        except Exception as e:
            return json.dumps({"error": f"Summary creation failed: {str(e)}"}, indent=2)
