from crewai import Agent, Task
from tools.vision_tools import VisionExtractor
from models.data_models import Document<PERSON>hunk, ExtractionResult
from typing import List
import asyncio
from concurrent.futures import ThreadPoolExecutor

class ExtractionAgent:
    """Agent responsible for layout detection and data extraction using GPT-4V"""
    
    def __init__(self):
        self.extractor = VisionExtractor()
        self.agent = Agent(
            role="Vision-based Data Extractor",
            goal="Use GPT-4V to identify layouts and extract relevant portfolio data from document chunks",
            backstory="""You are an expert financial document analyzer with advanced vision capabilities. 
            You specialize in identifying tabular data structures, understanding various document layouts, 
            and extracting portfolio and investment information from documents with different formats. 
            You can handle both structured and unstructured financial documents.""",
            verbose=True,
            allow_delegation=False
        )
    
    def create_extraction_task(self, chunks: List[DocumentChunk]) -> Task:
        """Create a task for data extraction"""
        return Task(
            description=f"""
            Analyze {len(chunks)} document chunks using GPT-4V vision capabilities to extract portfolio data.
            
            Requirements:
            1. Process each chunk through GPT-4V for layout detection
            2. Identify and extract tabular financial data
            3. Look for portfolio information including:
               - Document metadata (advisor, client, portfolio ID, etc.)
               - Investment holdings (ticker, shares, values, etc.)
            4. Handle parallel processing of chunks efficiently
            5. Map various field names to standard format
            6. Extract data even from non-uniform document formats
            
            Focus on finding:
            - Ticker/Symbol (REQUIRED)
            - Shares/Quantity (REQUIRED) 
            - Account numbers, values, cost basis (optional)
            - Document metadata (advisor, client names, dates)
            
            Input: List of DocumentChunk objects with image data
            Output: List of ExtractionResult objects with extracted data
            """,
            agent=self.agent,
            expected_output="List of ExtractionResult objects containing extracted portfolio data"
        )
    
    def extract_from_chunks(self, chunks: List[DocumentChunk]) -> List[ExtractionResult]:
        """Extract data from all chunks using parallel processing"""
        try:
            results = []
            
            # Process chunks in parallel for better performance
            with ThreadPoolExecutor(max_workers=3) as executor:
                futures = []
                
                for chunk in chunks:
                    if chunk.image_data:
                        future = executor.submit(
                            self.extractor.extract_portfolio_data,
                            chunk.chunk_id,
                            chunk.image_data,
                            chunk.content
                        )
                        futures.append(future)
                
                # Collect results
                for future in futures:
                    try:
                        result = future.result(timeout=60)  # 60 second timeout per chunk
                        results.append(result)
                    except Exception as e:
                        print(f"Error processing chunk: {str(e)}")
                        # Create error result
                        error_result = ExtractionResult(
                            chunk_id="unknown",
                            extracted_data=[],
                            confidence_score=0.0,
                            errors=[f"Processing failed: {str(e)}"],
                            warnings=[]
                        )
                        results.append(error_result)
            
            return results
            
        except Exception as e:
            print(f"Error in extraction agent: {str(e)}")
            return []
    
    def extract_from_single_chunk(self, chunk: DocumentChunk) -> ExtractionResult:
        """Extract data from a single chunk"""
        try:
            if not chunk.image_data:
                return ExtractionResult(
                    chunk_id=chunk.chunk_id,
                    extracted_data=[],
                    confidence_score=0.0,
                    errors=["No image data available for vision processing"],
                    warnings=[]
                )
            
            return self.extractor.extract_portfolio_data(
                chunk.chunk_id,
                chunk.image_data,
                chunk.content
            )
            
        except Exception as e:
            return ExtractionResult(
                chunk_id=chunk.chunk_id,
                extracted_data=[],
                confidence_score=0.0,
                errors=[f"Extraction failed: {str(e)}"],
                warnings=[]
            )
